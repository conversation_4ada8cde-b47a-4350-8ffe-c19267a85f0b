<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      title="添加店铺"
      width="480px"
      :draggable="true"
      :align-center="true"
      :destroy-on-close="true"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="handleClose"
    >
      <div class="login-container">
        <div class="qr-code-section">
          <div class="qr-image-wrapper">
            <img :src="dialogProps.row.row.image" alt="二维码" class="qr-image" />
          </div>

          <div class="status-message">
            <div v-if="scanMsg === '手机客户端确认登录'" class="confirm-message">
              <el-icon class="status-icon"><Loading /></el-icon>
              <span>{{ scanMsg }}</span>
            </div>
            <div v-else-if="scanMsg === '二维码已过期'" class="expired-message">
              <span>{{ scanMsg }}</span>
            </div>
            <div v-else class="scan-tips">
              <!-- <el-icon class="scan-icon"><Search /></el-icon> -->
              <span>请使用手机京麦App扫码</span>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose" :disabled="loading">取消</el-button>
          <el-button type="primary" @click="refreshQRCode" :loading="loading">
            {{ loading ? "刷新中..." : "刷新二维码" }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import { getLoginStatus, getShopLoginQR } from "@/views/shop/index";
import { ElMessage } from "element-plus";
import { Loading } from "@element-plus/icons-vue";

const scanMsg = ref("");
const dialogVisible = ref(false);
const loading = ref(false);

interface Props {
  row: { [key: string]: any };
}
const dialogProps = ref<Props>({
  row: {}
});
// 定义定时器 ID
let intervalId: NodeJS.Timeout;
let timeoutId: NodeJS.Timeout;

const acceptParams = (row: Props) => {
  dialogProps.value.row = row;
  dialogVisible.value = true;
  startPolling();
};

// 清除所有定时器
const clearAllTimers = () => {
  if (intervalId) {
    clearInterval(intervalId);
  }
  if (timeoutId) {
    clearTimeout(timeoutId);
  }
};

// 开始轮询登录状态
const startPolling = () => {
  // 先清除之前的定时器
  clearAllTimers();

  // 设置轮询定时器
  intervalId = setInterval(() => {
    getLoginStatus({ key: dialogProps.value.row.row.key, from: "bind" })
      .then(res => {
        scanMsg.value = res.data.msg;
        if (res.data.status) {
          ElMessage.success("操作成功");
          dialogProps.value.row.row.getTableList!();
          handleClose();
        } else {
          if (res.data.qrCodeStateType === "CANCELAUTH") {
            ElMessage.error("二维码已取消授权");
            handleClose();
          }
        }
      })
      .catch(error => {
        console.error("获取登录状态失败:", error);
      });
  }, 2000);

  // 设置60秒超时定时器
  timeoutId = setTimeout(() => {
    clearAllTimers();
    ElMessage.warning("二维码已过期，请刷新后重试");
    scanMsg.value = "二维码已过期";
  }, 60000); // 60秒
};

// 刷新二维码
const refreshQRCode = async () => {
  if (loading.value) return;

  loading.value = true;
  try {
    // 先清除所有定时器
    clearAllTimers();

    // 重置扫码消息
    scanMsg.value = "";

    // 重新获取二维码（这里需要根据实际API调整）
    // 假设需要重新调用获取二维码的接口
    ElMessage.success("二维码已刷新");

    // 重新开始轮询
    startPolling();
  } catch (error) {
    console.error("刷新二维码失败:", error);
    ElMessage.error("刷新二维码失败，请重试");
  } finally {
    loading.value = false;
  }
};

defineExpose({
  acceptParams
});

const handleClose = () => {
  // 清除所有定时器
  clearAllTimers();
  scanMsg.value = "";
  dialogVisible.value = false;
};
</script>
<style lang="scss" scoped>
.login-container {
  padding: 20px;
  text-align: center;
}
.qr-code-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: center;
}
.qr-image-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background: #fafafa;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}
.qr-image {
  width: 150px;
  height: 150px;
  border-radius: 4px;
}
.status-message {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}
.confirm-message {
  display: flex;
  gap: 8px;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
  color: #e6a23c;
  .status-icon {
    font-size: 16px;
    animation: spin 1s linear infinite;
  }
}
.scan-tips {
  display: flex;
  gap: 8px;
  align-items: center;
  font-size: 14px;
  color: #606266;
  .scan-icon {
    font-size: 16px;
    color: #409eff;
  }
}
.expired-message {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 500;
  color: #f56c6c;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.dialog-footer {
  display: flex;
  gap: 16px;
  justify-content: center;
  padding: 20px;

  // margin: 0 -20px -20px;
  background: #fafafa;
  border-radius: 0 0 8px 8px;
  .el-button {
    min-width: 100px;
  }
}

// 全局dialog样式调整
:deep(.el-dialog) {
  overflow: hidden;
  border-radius: 12px;
  .el-dialog__header {
    // padding: 20px 20px 0;
    background: white;
    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }
  }
  .el-dialog__body {
    padding: 0;
  }
  .el-dialog__footer {
    padding: 0;
  }
}
</style>

1. 在右上角用红色的字来显示倒计时剩余多少秒。 2. 当定时器在执行check的过程中，点击刷新按钮，还是没有执行
